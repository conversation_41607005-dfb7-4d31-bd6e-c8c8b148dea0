<?php
use Cake\Routing\Route\DashedRoute;
use Cake\Routing\RouteBuilder;

return function (RouteBuilder $routes): void {
    $routes->setRouteClass(DashedRoute::class);

    // 👇 Toutes les routes de l'application
    $routes->scope('/', function (RouteBuilder $builder): void {
        // Page d'accueil
        $builder->connect('/', ['controller' => 'Pages', 'action' => 'display', 'home']);
        $builder->connect('/pages/*', 'Pages::display');

        // Routes personnalisées pour Users
        $builder->connect('/users/login', ['controller' => 'Users', 'action' => 'login']);
        $builder->connect('/users/register', ['controller' => 'Users', 'action' => 'register'], ['_method' => ['POST', 'OPTIONS']]);

        // 🔁 Déclare que tu veux gérer du JSON
        $builder->setExtensions(['json']);

        // ✅ Routes RESTful automatiques pour chaque entité
        $builder->resources('Users');
        $builder->resources('Banques');
        $builder->resources('Comptes');
        $builder->resources('Personnels');
        $builder->resources('Operations');
        $builder->resources('Types'); // si tu as une entité Types par ex.

        // Routes de secours (fallbacks)
        $builder->fallbacks(DashedRoute::class);
    });
};
