import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Eye, EyeOff } from 'lucide-react';
import { authService } from '../services/authService';

const Register = () => {
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [fieldErrors, setFieldErrors] = useState({});
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    username: '',
    password: '',
    confirmPassword: '',
    nom: '',
    tel: '',
    mail: '',
    adresse: '',
    date_naissance: ''
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    setFieldErrors({ ...fieldErrors, [e.target.name]: '' });
  };

  const validateStepOne = () => {
    const errors = {};
    if (!formData.username.trim()) errors.username = 'Username is required';
    if (!formData.password) errors.password = 'Password is required';
    if (formData.password !== formData.confirmPassword) errors.confirmPassword = 'Passwords do not match';
    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const validateStepTwo = () => {
    const errors = {};
    if (!formData.nom.trim()) errors.nom = 'Nom is required';
    if (!formData.tel.trim()) errors.tel = 'Téléphone requis';
    if (!formData.mail.trim()) errors.mail = 'Email requis';
    if (!formData.adresse.trim()) errors.adresse = 'Adresse requise';
    if (!formData.date_naissance.trim()) errors.date_naissance = 'Date requise';
    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleNext = () => {
    if (validateStepOne()) setStep(2);
  };

  const handleBack = () => setStep(1);

 const handleSubmit = async (e) => {
    e.preventDefault();

    const errors = {};
    // Step 1 validation
    if (!formData.username.trim()) errors.username = 'Username is required';
    if (!formData.password) errors.password = 'Password is required';
    if (formData.password !== formData.confirmPassword) errors.confirmPassword = 'Passwords do not match';
    
    // Step 2 validation
    if (!formData.nom.trim()) errors.nom = 'Nom is required';
    if (!formData.tel.trim()) errors.tel = 'Téléphone requis';
    if (!formData.mail.trim()) errors.mail = 'Email requis';
    if (!formData.adresse.trim()) errors.adresse = 'Adresse requise';
    if (!formData.date_naissance.trim()) errors.date_naissance = 'Date requise';

    setFieldErrors(errors);

    if (Object.keys(errors).length > 0) {
      const stepOneErrorKeys = ['username', 'password', 'confirmPassword'];
      const hasStepOneError = stepOneErrorKeys.some(key => key in errors);
      if (hasStepOneError) {
        setStep(1);
      }
      return;
    }

    setLoading(true);
    setError('');

    const { username, password, nom, tel, mail, adresse, date_naissance } = formData;

    const dataToSend = {
      user: {
        username,
        password,
        role: 'user',
      },
      personnel: {
        nom,
        tel,
        mail,
        adresse,
        date_naissance,
      },
    };

    try {
      const response = await authService.register(dataToSend);
      if (response.success) {
        navigate('/login');
      } else {
        setError(response.error || 'Registration failed');
      }
    } catch (err) {
      setError('Server error, please try again.');
    } finally {
      setLoading(false);
    }
  };


  return (
    <div className="min-h-screen flex items-center justify-center p-4 relative">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-4xl mx-auto"
      >
        <div className="glass-card form-container">
          <h1 className="text-3xl font-bold text-white text-center mb-6">Register</h1>

          {error && <div className="bg-red-500/20 p-4 rounded text-red-300 text-sm mb-4">{error}</div>}

          <form onSubmit={handleSubmit}>
            {step === 1 && (
              <>

                <input type="text" name="username" placeholder="Username" value={formData.username} onChange={handleChange} className="glass-input" />
                {fieldErrors.username && <p className="field-error">{fieldErrors.username}</p>}

                <div className="password-input-container">
                  <input type={showPassword ? 'text' : 'password'} name="password" placeholder="Password" value={formData.password} onChange={handleChange} className="glass-input" />
                  <button
                  type="button"
                  className="password-toggle"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
                </div>
                {fieldErrors.password && <p className="field-error">{fieldErrors.password}</p>}

                <div className="password-input-container">
                  <input type={showConfirmPassword ? 'text' : 'password'} name="confirmPassword" placeholder="Confirm Password" value={formData.confirmPassword} onChange={handleChange} className="glass-input" />
                  <button
                  type="button"
                  className="password-toggle"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
                </div>
                {fieldErrors.confirmPassword && <p className="field-error">{fieldErrors.confirmPassword}</p>}

                <button type="button" className="glass-button mt-4" onClick={handleNext}>Next</button>
              </>
            )}

            {step === 2 && (
              <>
                <input type="text" name="nom" placeholder="Nom" value={formData.nom} onChange={handleChange} className="glass-input" />
                {fieldErrors.nom && <p className="field-error">{fieldErrors.nom}</p>}

                <input type="text" name="tel" placeholder="Téléphone" value={formData.tel} onChange={handleChange} className="glass-input" />
                {fieldErrors.tel && <p className="field-error">{fieldErrors.tel}</p>}

                <input type="email" name="mail" placeholder="Email" value={formData.mail} onChange={handleChange} className="glass-input" />
                {fieldErrors.mail && <p className="field-error">{fieldErrors.mail}</p>}

                <input type="text" name="adresse" placeholder="Adresse" value={formData.adresse} onChange={handleChange} className="glass-input" />
                {fieldErrors.adresse && <p className="field-error">{fieldErrors.adresse}</p>}

                <input type="date" name="date_naissance" placeholder="Date de naissance" value={formData.date_naissance} onChange={handleChange} className="glass-input" />
                {fieldErrors.date_naissance && <p className="field-error">{fieldErrors.date_naissance}</p>}

                <div className="flex gap-4 mt-4">
                  <button type="button" className="glass-button bg-slate-500" onClick={handleBack}>Back</button>
                  <button type="submit" className="glass-button" disabled={loading}>{loading ? 'Creating...' : 'Register'}</button>
                </div>
              </>
            )}
          </form>

          <div className="form-footer">
            <p>
              Already have an account?{' '}
              <button
                onClick={() => navigate('/login')}
                className="auth-link"
              >
                Login
              </button>
            </p>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default Register;
