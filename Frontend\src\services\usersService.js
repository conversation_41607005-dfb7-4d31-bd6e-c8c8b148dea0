import axios from 'axios';

const API_BASE_URL = 'http://localhost:8765';

export const getUsers = async (page = 1) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/users?page=${page}`);
    return response.data; // { users: [...], pagination: { page, pages, total } }
  } catch (error) {
    console.error('Erreur getUsers :', error);
    return { users: [], pagination: { page: 1, pages: 1, total: 0 } };
  }
};

export const deleteUser = async (id) => {
  try {
    await axios.delete(`${API_BASE_URL}/users/${id}`);
  } catch (error) {
    // gérer erreur si besoin
  }
};

export const createUser = async (data) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/users`, data);
    return { success: true, user: response.data };
  } catch (error) {
    return { success: false, error: error.response?.data?.message || 'Erreur lors de la création' };
  }
};

export const updateUser = async (id, data) => {
  try {
    const response = await axios.put(`${API_BASE_URL}/users/${id}`, data);
    return { success: true, user: response.data };
  } catch (error) {
    return { success: false, error: error.response?.data?.message || 'Erreur lors de la modification' };
  }
};
