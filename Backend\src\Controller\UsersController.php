<?php
declare(strict_types=1);

namespace App\Controller;

class UsersController extends ApiController
{
    
    public function index()
    {
        try {
            $query = $this->Users->find();
            $users = $this->paginate($query);

            return $this->respondJson([
                'success' => true,
                'data' => $users,
                'pagination' => [
                    'page' => $this->request->getParam('paging.Users.page'),
                    'count' => $this->request->getParam('paging.Users.count'),
                    'total' => $this->request->getParam('paging.Users.total')
                ]
            ]);
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    public function view($id = null)
    {
        try {
            if (!$id || !is_numeric($id)) {
                throw new \InvalidArgumentException('Invalid user ID');
            }

            $user = $this->Users->get($id);
            
            return $this->respondJson([
                'success' => true,
                'data' => $user
            ]);
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    public function add()
    {
        try {
            if (!$this->request->is('post')) {
                return $this->respondJson([
                    'success' => false,
                    'message' => 'Method not allowed'
                ], 405);
            }

            $requestData = $this->validateJsonRequest();
            
            if (empty($requestData)) {
                throw new \InvalidArgumentException('Request data is required');
            }

            $user = $this->Users->newEmptyEntity();
            $user = $this->Users->patchEntity($user, $requestData);
            
            if ($this->Users->save($user)) {
                return $this->respondJson([
                    'success' => true,
                    'message' => 'User created successfully',
                    'data' => $user
                ], 201);
            }

            return $this->respondJson([
                'success' => false,
                'message' => 'User could not be saved',
                'errors' => $user->getErrors()
            ], 422);
            
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    public function edit($id = null)
    {
        try {
            if (!$this->request->is(['patch', 'post', 'put'])) {
                return $this->respondJson([
                    'success' => false,
                    'message' => 'Method not allowed'
                ], 405);
            }

            if (!$id || !is_numeric($id)) {
                throw new \InvalidArgumentException('Invalid user ID');
            }

            $requestData = $this->validateJsonRequest();
            
            if (empty($requestData)) {
                throw new \InvalidArgumentException('Request data is required');
            }

            $user = $this->Users->get($id);
            $user = $this->Users->patchEntity($user, $requestData);
            
            if ($this->Users->save($user)) {
                return $this->respondJson([
                    'success' => true,
                    'message' => 'User updated successfully',
                    'data' => $user
                ]);
            }

            return $this->respondJson([
                'success' => false,
                'message' => 'User could not be updated',
                'errors' => $user->getErrors()
            ], 422);
            
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    public function delete($id = null)
    {
        try {
            if (!$this->request->is(['post', 'delete'])) {
                return $this->respondJson([
                    'success' => false,
                    'message' => 'Method not allowed'
                ], 405);
            }

            if (!$id || !is_numeric($id)) {
                throw new \InvalidArgumentException('Invalid user ID');
            }

            $user = $this->Users->get($id);
            
            if ($this->Users->delete($user)) {
                return $this->respondJson([
                    'success' => true,
                    'message' => 'User deleted successfully'
                ]);
            }

            return $this->respondJson([
                'success' => false,
                'message' => 'User could not be deleted'
            ], 422);
            
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    public function register()
    {
        try {
            if (!$this->request->is('post')) {
                return $this->respondJson([
                    'success' => false,
                    'message' => 'Method not allowed'
                ], 405);
            }

            $requestData = $this->validateJsonRequest();

            // Validate required structure
            if (!isset($requestData['user']) || !isset($requestData['personnel'])) {
                throw new \InvalidArgumentException('Invalid data structure. Required fields: user, personnel');
            }

            $userData = $requestData['user'];
            $personnelData = $requestData['personnel'];

            // Validate required user fields
            if (empty($userData['username']) || empty($userData['password'])) {
                throw new \InvalidArgumentException('Username and password are required');
            }

            // Validate password strength (optional)
            if (strlen($userData['password']) < 6) {
                throw new \InvalidArgumentException('Password must be at least 6 characters long');
            }

            // Check if username already exists
            $existingUser = $this->Users->find()
                ->where(['username' => $userData['username']])
                ->first();
            
            if ($existingUser) {
                return $this->respondJson([
                    'success' => false,
                    'message' => 'Username already exists'
                ], 409); // Conflict
            }

            // Hash password
            $userData['password'] = password_hash($userData['password'], PASSWORD_DEFAULT);

            // Merge data
            $fullData = $userData;
            $fullData['personnels'][] = $personnelData;

            $user = $this->Users->patchEntity(
                $this->Users->newEmptyEntity(),
                $fullData,
                ['associated' => ['Personnels']]
            );

            if ($this->Users->save($user)) {
                // Remove password from response
                $responseUser = $user->toArray();
                unset($responseUser['password']);
                
                return $this->respondJson([
                    'success' => true,
                    'message' => 'Registration successful',
                    'data' => $responseUser
                ], 201);
            }

            return $this->respondJson([
                'success' => false,
                'message' => 'Registration failed',
                'errors' => $user->getErrors()
            ], 422);
            
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    public function login()
    {
        try {
            if (!$this->request->is('post')) {
                return $this->respondJson([
                    'success' => false,
                    'message' => 'Method not allowed'
                ], 405);
            }

            $requestData = $this->validateJsonRequest();
            
            $username = $requestData['username'] ?? '';
            $password = $requestData['password'] ?? '';

            if (empty($username) || empty($password)) {
                throw new \InvalidArgumentException('Username and password are required');
            }

            $user = $this->Users->find()
                ->where(['username' => $username])
                ->first();

            if (!$user || !password_verify($password, $user->password)) {
                return $this->respondJson([
                    'success' => false,
                    'message' => 'Invalid credentials'
                ], 401);
            }

            // Create session or token here if needed
            $this->request->getSession()->write('Auth.User', [
                'id' => $user->id,
                'username' => $user->username
            ]);

            return $this->respondJson([
                'success' => true,
                'message' => 'Login successful',
                'user' => [
                    'id' => $user->id,
                    'username' => $user->username,
                    'created' => $user->created
                ]
            ]);
            
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    public function logout()
    {
        try {
            $session = $this->request->getSession();
            
            if ($session->check('Auth.User')) {
                $session->delete('Auth.User');
                $session->destroy();
                
                return $this->respondJson([
                    'success' => true,
                    'message' => 'Logout successful'
                ]);
            }

            return $this->respondJson([
                'success' => false,
                'message' => 'No active session found'
            ], 400);
            
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    /**
     * Get current authenticated user
     */
    public function me()
    {
        try {
            $session = $this->request->getSession();
            $authUser = $session->read('Auth.User');
            
            if (!$authUser) {
                return $this->respondJson([
                    'success' => false,
                    'message' => 'Not authenticated'
                ], 401);
            }

            $user = $this->Users->get($authUser['id']);
            
            return $this->respondJson([
                'success' => true,
                'data' => [
                    'id' => $user->id,
                    'username' => $user->username,
                    'created' => $user->created,
                    'modified' => $user->modified
                ]
            ]);
            
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }
}