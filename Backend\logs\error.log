2025-07-16 19:45:11 error: [Cake\Http\Exception\InvalidCsrfTokenException] Missing or incorrect CSRF cookie type. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\Middleware\CsrfProtectionMiddleware.php on line 354
Stack Trace:
- CORE\src\Http\Middleware\CsrfProtectionMiddleware.php:165
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Middleware\BodyParserMiddleware.php:172
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/register
2025-07-16 19:46:00 error: [Cake\Http\Exception\InvalidCsrfTokenException] Missing or incorrect CSRF cookie type. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\Middleware\CsrfProtectionMiddleware.php on line 354
Stack Trace:
- CORE\src\Http\Middleware\CsrfProtectionMiddleware.php:165
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Middleware\BodyParserMiddleware.php:172
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/register
2025-07-16 19:47:28 error: [Cake\Http\Exception\InvalidCsrfTokenException] Missing or incorrect CSRF cookie type. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\Middleware\CsrfProtectionMiddleware.php on line 354
Stack Trace:
- CORE\src\Http\Middleware\CsrfProtectionMiddleware.php:165
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Middleware\BodyParserMiddleware.php:172
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/register
2025-07-16 21:11:16 error: [Cake\Http\Exception\MethodNotAllowedException] Method Not Allowed in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\ServerRequest.php on line 1442
Stack Trace:
- APP/Controller\UsersController.php:95
- CORE\src\Controller\Controller.php:505
- CORE\src\Controller\ControllerFactory.php:166
- CORE\src\Controller\ControllerFactory.php:141
- CORE\src\Http\BaseApplication.php:362
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/login
Referer URL: http://localhost:5173/
2025-07-16 21:13:00 error: [Cake\Http\Exception\MethodNotAllowedException] Method Not Allowed in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\ServerRequest.php on line 1442
Stack Trace:
- APP/Controller\UsersController.php:95
- CORE\src\Controller\Controller.php:505
- CORE\src\Controller\ControllerFactory.php:166
- CORE\src\Controller\ControllerFactory.php:141
- CORE\src\Http\BaseApplication.php:362
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/login
Referer URL: http://localhost:5173/
2025-07-16 21:13:24 error: [Cake\Http\Exception\MethodNotAllowedException] Method Not Allowed in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\ServerRequest.php on line 1442
Stack Trace:
- APP/Controller\UsersController.php:95
- CORE\src\Controller\Controller.php:505
- CORE\src\Controller\ControllerFactory.php:166
- CORE\src\Controller\ControllerFactory.php:141
- CORE\src\Http\BaseApplication.php:362
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/login
Referer URL: http://localhost:5173/
2025-07-16 21:17:03 error: [Cake\Http\Exception\MethodNotAllowedException] Method Not Allowed in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\ServerRequest.php on line 1442
Stack Trace:
- APP/Controller\UsersController.php:95
- CORE\src\Controller\Controller.php:505
- CORE\src\Controller\ControllerFactory.php:166
- CORE\src\Controller\ControllerFactory.php:141
- CORE\src\Http\BaseApplication.php:362
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/login
Referer URL: http://localhost:5174/
2025-07-16 21:17:20 error: [Cake\Http\Exception\MethodNotAllowedException] Method Not Allowed in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\ServerRequest.php on line 1442
Stack Trace:
- APP/Controller\UsersController.php:66
- CORE\src\Controller\Controller.php:505
- CORE\src\Controller\ControllerFactory.php:166
- CORE\src\Controller\ControllerFactory.php:141
- CORE\src\Http\BaseApplication.php:362
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/register
Referer URL: http://localhost:5174/
2025-07-22 10:02:28 error: [Cake\Database\Exception\MissingConnectionException] Connection to Mysql could not be established: SQLSTATE[HY000] [2002] Aucune connexion n’a pu être établie car l’ordinateur cible l’a expressément refusée in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Database\Driver.php on line 202
Exception Attributes: array (
  'driver' => 'Mysql',
  'reason' => 'SQLSTATE[HY000] [2002] Aucune connexion n’a pu être établie car l’ordinateur cible l’a expressément refusée',
)
Stack Trace:
- CORE\src\Database\Driver\Mysql.php:158
- CORE\src\Database\Schema\SchemaDialect.php:60
- CORE\src\Database\Driver\Mysql.php:204
- CORE\src\Database\Schema\Collection.php:97
- CORE\src\Database\Schema\Collection.php:87
- CORE\src\Database\Schema\CachedCollection.php:107
- CORE\src\ORM\Table.php:526
- CORE\src\ORM\Query\CommonQueryTrait.php:48
- CORE\src\ORM\Query\SelectQuery.php:202
- CORE\src\ORM\Query\QueryFactory.php:34
- CORE\src\ORM\Table.php:1748
- CORE\src\ORM\Table.php:1279
- APP/Controller\UsersController.php:21
- CORE\src\Controller\Controller.php:505
- CORE\src\Controller\ControllerFactory.php:166
- CORE\src\Controller\ControllerFactory.php:141
- CORE\src\Http\BaseApplication.php:362
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Caused by: [PDOException] SQLSTATE[HY000] [2002] Aucune connexion n’a pu être établie car l’ordinateur cible l’a expressément refusée in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Database\Driver.php on line 191
Stack Trace:
- CORE\src\Database\Driver.php:191
- CORE\src\Core\Retry\CommandRetry.php:71
- CORE\src\Database\Driver.php:200
- CORE\src\Database\Driver\Mysql.php:158
- CORE\src\Database\Schema\SchemaDialect.php:60
- CORE\src\Database\Driver\Mysql.php:204
- CORE\src\Database\Schema\Collection.php:97
- CORE\src\Database\Schema\Collection.php:87
- CORE\src\Database\Schema\CachedCollection.php:107
- CORE\src\ORM\Table.php:526
- CORE\src\ORM\Query\CommonQueryTrait.php:48
- CORE\src\ORM\Query\SelectQuery.php:202
- CORE\src\ORM\Query\QueryFactory.php:34
- CORE\src\ORM\Table.php:1748
- CORE\src\ORM\Table.php:1279
- APP/Controller\UsersController.php:21
- CORE\src\Controller\Controller.php:505
- CORE\src\Controller\ControllerFactory.php:166
- CORE\src\Controller\ControllerFactory.php:141
- CORE\src\Http\BaseApplication.php:362
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users
2025-07-22 10:02:56 error: [Cake\Http\Exception\MethodNotAllowedException] Method Not Allowed in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\ServerRequest.php on line 1442
Stack Trace:
- APP/Controller\UsersController.php:66
- CORE\src\Controller\Controller.php:505
- CORE\src\Controller\ControllerFactory.php:166
- CORE\src\Controller\ControllerFactory.php:141
- CORE\src\Http\BaseApplication.php:362
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/register
2025-07-22 10:03:08 error: [Cake\Http\Exception\MethodNotAllowedException] Method Not Allowed in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\ServerRequest.php on line 1442
Stack Trace:
- APP/Controller\UsersController.php:100
- CORE\src\Controller\Controller.php:505
- CORE\src\Controller\ControllerFactory.php:166
- CORE\src\Controller\ControllerFactory.php:141
- CORE\src\Http\BaseApplication.php:362
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/login
2025-07-22 10:05:49 error: [Cake\Http\Exception\MethodNotAllowedException] Method Not Allowed in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\ServerRequest.php on line 1442
Stack Trace:
- APP/Controller\UsersController.php:66
- CORE\src\Controller\Controller.php:505
- CORE\src\Controller\ControllerFactory.php:166
- CORE\src\Controller\ControllerFactory.php:141
- CORE\src\Http\BaseApplication.php:362
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/register
2025-07-22 10:22:29 error: [Cake\Http\Exception\MethodNotAllowedException] Method Not Allowed in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\ServerRequest.php on line 1442
Stack Trace:
- APP/Controller\UsersController.php:66
- CORE\src\Controller\Controller.php:505
- CORE\src\Controller\ControllerFactory.php:166
- CORE\src\Controller\ControllerFactory.php:141
- CORE\src\Http\BaseApplication.php:362
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/register
2025-07-22 22:45:15 error: [Cake\Http\Exception\MissingControllerException] Controller class `Banque` could not be found. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Controller\ControllerFactory.php on line 335
Exception Attributes: array (
  'controller' => 'Banque',
  'plugin' => NULL,
  'prefix' => NULL,
  '_ext' => NULL,
)
Stack Trace:
- CORE\src\Controller\ControllerFactory.php:77
- CORE\src\Http\BaseApplication.php:360
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /banque
2025-07-23 19:23:00 error: [Cake\Controller\Exception\MissingComponentException] Component class `PaginatorComponent` could not be found. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Controller\ComponentRegistry.php on line 130
Exception Attributes: array (
  'class' => 'PaginatorComponent',
  'plugin' => NULL,
)
Stack Trace:
- CORE\src\Core\ObjectRegistry.php:111
- CORE\src\Controller\Controller.php:284
- APP/Controller\BanquesController.php:16
- CORE\src\Controller\Controller.php:238
- [internal]:??
- CORE\src\Controller\ControllerFactory.php:111
- CORE\src\Http\BaseApplication.php:360
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /banques
2025-07-23 19:23:17 error: [Cake\Controller\Exception\MissingComponentException] Component class `PaginatorComponent` could not be found. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Controller\ComponentRegistry.php on line 130
Exception Attributes: array (
  'class' => 'PaginatorComponent',
  'plugin' => NULL,
)
Stack Trace:
- CORE\src\Core\ObjectRegistry.php:111
- CORE\src\Controller\Controller.php:284
- APP/Controller\BanquesController.php:16
- CORE\src\Controller\Controller.php:238
- [internal]:??
- CORE\src\Controller\ControllerFactory.php:111
- CORE\src\Http\BaseApplication.php:360
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /banques
2025-07-23 19:23:35 error: [Cake\Controller\Exception\MissingComponentException] Component class `PaginatorComponent` could not be found. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Controller\ComponentRegistry.php on line 130
Exception Attributes: array (
  'class' => 'PaginatorComponent',
  'plugin' => NULL,
)
Stack Trace:
- CORE\src\Core\ObjectRegistry.php:111
- CORE\src\Controller\Controller.php:284
- APP/Controller\BanquesController.php:16
- CORE\src\Controller\Controller.php:238
- [internal]:??
- CORE\src\Controller\ControllerFactory.php:111
- CORE\src\Http\BaseApplication.php:360
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /banques
2025-07-23 19:25:00 error: [Cake\Controller\Exception\MissingComponentException] Component class `PaginatorComponent` could not be found. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Controller\ComponentRegistry.php on line 130
Exception Attributes: array (
  'class' => 'PaginatorComponent',
  'plugin' => NULL,
)
Stack Trace:
- CORE\src\Core\ObjectRegistry.php:111
- CORE\src\Controller\Controller.php:284
- APP/Controller\BanquesController.php:21
- CORE\src\Controller\Controller.php:238
- [internal]:??
- CORE\src\Controller\ControllerFactory.php:111
- CORE\src\Http\BaseApplication.php:360
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /banques
2025-07-23 19:25:32 error: [Cake\Controller\Exception\MissingComponentException] Component class `PaginatorComponent` could not be found. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Controller\ComponentRegistry.php on line 130
Exception Attributes: array (
  'class' => 'PaginatorComponent',
  'plugin' => NULL,
)
Stack Trace:
- CORE\src\Core\ObjectRegistry.php:111
- CORE\src\Controller\Controller.php:284
- APP/Controller\BanquesController.php:21
- CORE\src\Controller\Controller.php:238
- [internal]:??
- CORE\src\Controller\ControllerFactory.php:111
- CORE\src\Http\BaseApplication.php:360
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /banques
2025-07-23 19:26:52 error: [Cake\Controller\Exception\MissingComponentException] Component class `RequestHandlerComponent` could not be found. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Controller\ComponentRegistry.php on line 130
Exception Attributes: array (
  'class' => 'RequestHandlerComponent',
  'plugin' => NULL,
)
Stack Trace:
- CORE\src\Core\ObjectRegistry.php:111
- CORE\src\Controller\Controller.php:284
- APP/Controller\BanquesController.php:21
- CORE\src\Controller\Controller.php:505
- CORE\src\Controller\ControllerFactory.php:166
- CORE\src\Controller\ControllerFactory.php:141
- CORE\src\Http\BaseApplication.php:362
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /banques
2025-07-23 19:34:50 error: [Error] Class "App\Controller\ApiController" not found in C:\Users\<USER>\Desktop\stage\Backend\src\Controller\BanquesController.php on line 11
Stack Trace:
- ROOT\vendor\composer\ClassLoader.php:576
- ROOT\vendor\composer\ClassLoader.php:427
- [internal]:??
- CORE\src\Core\App.php:162
- CORE\src\Core\App.php:69
- CORE\src\Controller\ControllerFactory.php:324
- CORE\src\Controller\ControllerFactory.php:75
- CORE\src\Http\BaseApplication.php:360
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /banques
2025-07-23 19:38:14 error: [Cake\Http\Exception\MissingControllerException] Controller class `Banque` could not be found. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Controller\ControllerFactory.php on line 335
Exception Attributes: array (
  'controller' => 'Banque',
  'plugin' => NULL,
  'prefix' => NULL,
  '_ext' => NULL,
)
Stack Trace:
- CORE\src\Controller\ControllerFactory.php:77
- CORE\src\Http\BaseApplication.php:360
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /banque
2025-07-23 19:38:15 error: [Cake\Http\Exception\MissingControllerException] Controller class `Banque` could not be found. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Controller\ControllerFactory.php on line 335
Exception Attributes: array (
  'controller' => 'Banque',
  'plugin' => NULL,
  'prefix' => NULL,
  '_ext' => NULL,
)
Stack Trace:
- CORE\src\Controller\ControllerFactory.php:77
- CORE\src\Http\BaseApplication.php:360
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /banque
2025-07-23 20:02:53 error: [Error] Class "Authentication\Middleware\AuthenticationMiddleware" not found in C:\Users\<USER>\Desktop\stage\Backend\src\Application.php on line 86
Stack Trace:
- CORE\src\Http\Server.php:97
- ROOT\webroot\index.php:37
- [main]:

2025-07-23 20:14:18 error: [Cake\Controller\Exception\MissingComponentException] Component class `RequestHandlerComponent` could not be found. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Controller\ComponentRegistry.php on line 130
Exception Attributes: array (
  'class' => 'RequestHandlerComponent',
  'plugin' => NULL,
)
Stack Trace:
- CORE\src\Core\ObjectRegistry.php:111
- CORE\src\Controller\Controller.php:284
- APP/Controller\ApiController.php:14
- CORE\src\Controller\Controller.php:238
- [internal]:??
- CORE\src\Controller\ControllerFactory.php:111
- CORE\src\Http\BaseApplication.php:360
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /banques
2025-07-23 20:14:34 error: [Cake\Controller\Exception\MissingComponentException] Component class `RequestHandlerComponent` could not be found. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Controller\ComponentRegistry.php on line 130
Exception Attributes: array (
  'class' => 'RequestHandlerComponent',
  'plugin' => NULL,
)
Stack Trace:
- CORE\src\Core\ObjectRegistry.php:111
- CORE\src\Controller\Controller.php:284
- APP/Controller\ApiController.php:14
- CORE\src\Controller\Controller.php:238
- [internal]:??
- CORE\src\Controller\ControllerFactory.php:111
- CORE\src\Http\BaseApplication.php:360
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /banques
2025-07-23 20:15:29 error: [Cake\Controller\Exception\MissingComponentException] Component class `RequestHandlerComponent` could not be found. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Controller\ComponentRegistry.php on line 130
Exception Attributes: array (
  'class' => 'RequestHandlerComponent',
  'plugin' => NULL,
)
Stack Trace:
- CORE\src\Core\ObjectRegistry.php:111
- CORE\src\Controller\Controller.php:284
- APP/Controller\ApiController.php:14
- CORE\src\Controller\Controller.php:238
- [internal]:??
- CORE\src\Controller\ControllerFactory.php:111
- CORE\src\Http\BaseApplication.php:360
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /banques
2025-07-23 20:15:58 error: [Cake\Controller\Exception\MissingComponentException] Component class `RequestHandlerComponent` could not be found. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Controller\ComponentRegistry.php on line 130
Exception Attributes: array (
  'class' => 'RequestHandlerComponent',
  'plugin' => NULL,
)
Stack Trace:
- CORE\src\Core\ObjectRegistry.php:111
- CORE\src\Controller\Controller.php:284
- APP/Controller\ApiController.php:14
- CORE\src\Controller\Controller.php:238
- [internal]:??
- CORE\src\Controller\ControllerFactory.php:111
- CORE\src\Http\BaseApplication.php:360
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /banques
2025-07-23 20:18:13 error: [Cake\Controller\Exception\MissingComponentException] Component class `RequestHandlerComponent` could not be found. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Controller\ComponentRegistry.php on line 130
Exception Attributes: array (
  'class' => 'RequestHandlerComponent',
  'plugin' => NULL,
)
Stack Trace:
- CORE\src\Core\ObjectRegistry.php:111
- CORE\src\Controller\Controller.php:284
- APP/Controller\ApiController.php:14
- CORE\src\Controller\Controller.php:238
- [internal]:??
- CORE\src\Controller\ControllerFactory.php:111
- CORE\src\Http\BaseApplication.php:360
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:172
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /banques
2025-07-23 20:27:09 error: [Error] Call to undefined method App\Controller\BanquesController::loadModel() in C:\Users\<USER>\Desktop\stage\Backend\src\Controller\BanquesController.php on line 17
Stack Trace:
- CORE\src\Controller\Controller.php:238
- [internal]:??
- CORE\src\Controller\ControllerFactory.php:111
- CORE\src\Http\BaseApplication.php:360
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:172
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /banques
2025-07-23 20:28:35 error: [Cake\Controller\Exception\MissingComponentException] Component class `RequestHandlerComponent` could not be found. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Controller\ComponentRegistry.php on line 130
Exception Attributes: array (
  'class' => 'RequestHandlerComponent',
  'plugin' => NULL,
)
Stack Trace:
- CORE\src\Core\ObjectRegistry.php:111
- CORE\src\Controller\Controller.php:284
- APP/Controller\ApiController.php:14
- CORE\src\Controller\Controller.php:238
- [internal]:??
- CORE\src\Controller\ControllerFactory.php:111
- CORE\src\Http\BaseApplication.php:360
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:172
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /banques
2025-07-23 20:28:53 error: [Cake\Controller\Exception\MissingComponentException] Component class `RequestHandlerComponent` could not be found. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Controller\ComponentRegistry.php on line 130
Exception Attributes: array (
  'class' => 'RequestHandlerComponent',
  'plugin' => NULL,
)
Stack Trace:
- CORE\src\Core\ObjectRegistry.php:111
- CORE\src\Controller\Controller.php:284
- APP/Controller\ApiController.php:14
- CORE\src\Controller\Controller.php:238
- [internal]:??
- CORE\src\Controller\ControllerFactory.php:111
- CORE\src\Http\BaseApplication.php:360
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users
2025-07-23 22:15:35 error: [Cake\Controller\Exception\MissingComponentException] Component class `RequestHandlerComponent` could not be found. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Controller\ComponentRegistry.php on line 130
Exception Attributes: array (
  'class' => 'RequestHandlerComponent',
  'plugin' => NULL,
)
Stack Trace:
- CORE\src\Core\ObjectRegistry.php:111
- CORE\src\Controller\Controller.php:284
- APP/Controller\ApiController.php:15
- CORE\src\Controller\Controller.php:238
- [internal]:??
- CORE\src\Controller\ControllerFactory.php:111
- CORE\src\Http\BaseApplication.php:360
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:172
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users
2025-07-23 22:21:03 error: [Cake\Controller\Exception\MissingComponentException] Component class `RequestHandlerComponent` could not be found. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Controller\ComponentRegistry.php on line 130
Exception Attributes: array (
  'class' => 'RequestHandlerComponent',
  'plugin' => NULL,
)
Stack Trace:
- CORE\src\Core\ObjectRegistry.php:111
- CORE\src\Controller\Controller.php:284
- APP/Controller\ApiController.php:15
- APP/Controller\UsersController.php:13
- CORE\src\Controller\Controller.php:238
- [internal]:??
- CORE\src\Controller\ControllerFactory.php:111
- CORE\src\Http\BaseApplication.php:360
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:172
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users
2025-07-23 22:21:08 error: [Cake\Controller\Exception\MissingComponentException] Component class `RequestHandlerComponent` could not be found. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Controller\ComponentRegistry.php on line 130
Exception Attributes: array (
  'class' => 'RequestHandlerComponent',
  'plugin' => NULL,
)
Stack Trace:
- CORE\src\Core\ObjectRegistry.php:111
- CORE\src\Controller\Controller.php:284
- APP/Controller\ApiController.php:15
- APP/Controller\UsersController.php:13
- CORE\src\Controller\Controller.php:238
- [internal]:??
- CORE\src\Controller\ControllerFactory.php:111
- CORE\src\Http\BaseApplication.php:360
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:172
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users
2025-07-23 22:21:10 error: [Cake\Controller\Exception\MissingComponentException] Component class `RequestHandlerComponent` could not be found. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Controller\ComponentRegistry.php on line 130
Exception Attributes: array (
  'class' => 'RequestHandlerComponent',
  'plugin' => NULL,
)
Stack Trace:
- CORE\src\Core\ObjectRegistry.php:111
- CORE\src\Controller\Controller.php:284
- APP/Controller\ApiController.php:15
- APP/Controller\UsersController.php:13
- CORE\src\Controller\Controller.php:238
- [internal]:??
- CORE\src\Controller\ControllerFactory.php:111
- CORE\src\Http\BaseApplication.php:360
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:172
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users
