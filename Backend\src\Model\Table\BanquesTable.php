<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * Banques Model
 *
 * @property \App\Model\Table\ComptesTable&\Cake\ORM\Association\HasMany $Comptes
 *
 * @method \App\Model\Entity\Banque newEmptyEntity()
 * @method \App\Model\Entity\Banque newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Banque> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Banque get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Banque findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Banque patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Banque> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Banque|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Banque saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Banque>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Banque>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Banque>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Banque> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Banque>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Banque>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Banque>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Banque> deleteManyOrFail(iterable $entities, array $options = [])
 */
class BanquesTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('banques');
        $this->setDisplayField('nom');
        $this->setPrimaryKey('id');

        $this->hasMany('Comptes', [
            'foreignKey' => 'banque_id',
            'dependent' => true,
            'cascadeCallbacks' => true,
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('nom')
            ->maxLength('nom', 100)
            ->requirePresence('nom', 'create')
            ->notEmptyString('nom');

        return $validator;
    }
}
