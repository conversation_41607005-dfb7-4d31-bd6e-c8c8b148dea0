import axios from 'axios';

const API_BASE_URL = 'http://localhost:8765';

export const getPersonnels = async (page = 1) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/personnels?page=${page}`);
    return response.data; // { personnels: [...], pagination: { page, pages, total } }
  } catch (error) {
    console.error('Erreur getPersonnels :', error);
    return { personnels: [], pagination: { page: 1, pages: 1, total: 0 } };
  }
};


export const updatePersonnel = async (id, data) => {
  try {
    const response = await axios.put(`${API_BASE_URL}/personnels/${id}`, data);
    return { success: true, personnel: response.data };
  } catch (error) {
    return { success: false, error: error.response?.data?.message || 'Error' };
  }
};

export const deletePersonnel = async (id) => {
  try {
    await axios.delete(`${API_BASE_URL}/personnels/${id}`);
  } catch (error) {
    // handle error
  }
};

export const createPersonnel = async (data) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/personnels`, data);
    return { success: true, personnel: response.data };
  } catch (error) {
    return { success: false, error: error.response?.data?.message || 'Error' };
  }

  
};
