import { motion } from 'framer-motion';
import { LogOut } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { authService } from '../services/authService';

const Dashboard = () => {
  const navigate = useNavigate();
  const user = authService.getCurrentUser();

  const handleLogout = () => {
    authService.logout();
    navigate('/login');
  };

  return (
    <div className="flex items-center justify-center p-4 relative min-h-full">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="w-full max-w-4xl mx-auto"
      >
        <div className="glass-card form-container">
          {/* Header */}
          <div className="dashboard-header">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">Dashboard</h1>
              <p className="text-white/70">Welcome back, {user?.name || 'User'}!</p>
            </div>

            <button
              onClick={handleLogout}
              className="glass-button max-w-xs flex items-center gap-2"
            >
              <LogOut size={15} />
              Logout
            </button>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default Dashboard;
