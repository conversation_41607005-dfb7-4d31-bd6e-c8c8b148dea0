import { useEffect, useState } from 'react';
import GlassCard from '../components/GlassCard';
import Button from '../components/Button';
import { motion } from 'framer-motion';
import {
  getComptes,
  deleteCompte,
  createCompte,
  updateCompte,
} from '../services/comptesService';
import { getBanques } from '../services/banquesService';

const Comptes = () => {
  const [comptes, setComptes] = useState([]);
  const [banques, setBanques] = useState([]);
  const [showForm, setShowForm] = useState(false);
  const [editData, setEditData] = useState(null);
  const [formValues, setFormValues] = useState({
    rib: '',
    type: '',
    solde: '',
    banque_id: '',
  });

  // Pagination state backend
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const fetchData = async (page = 1) => {
    const data = await getComptes(page);
    if (data) {
      setComptes(data.data || []);
      setCurrentPage(data.pagination?.page || 1);
      setTotalPages(data.pagination?.pages || 1);
    } else {
      setComptes([]);
      setCurrentPage(1);
      setTotalPages(1);
    }
  };

  const fetchBanques = async () => {
    const data = await getBanques();
    setBanques(data);
  };

  useEffect(() => {
    fetchData(currentPage);
    fetchBanques();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    fetchData(currentPage);
  }, [currentPage]);

  const handleChange = (e) => {
    setFormValues({ ...formValues, [e.target.name]: e.target.value });
  };

  const handleAdd = () => {
    setEditData(null);
    setFormValues({
      rib: '',
      type: '',
      solde: '',
      banque_id: '',
    });
    setShowForm(true);
  };

  const handleEdit = (compte) => {
    setEditData(compte);
    setFormValues({
      rib: compte.rib,
      type: compte.type,
      solde: compte.solde,
      banque_id: compte.banque_id,
    });
    setShowForm(true);
  };

  const handleDelete = async (id) => {
    if (confirm('Voulez-vous vraiment supprimer ce compte ?')) {
      await deleteCompte(id);
      fetchData(currentPage);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editData) {
        await updateCompte(editData.id, formValues);
      } else {
        await createCompte(formValues);
      }
      setShowForm(false);
      fetchData(currentPage);
    } catch (error) {
      alert('Erreur lors de l’enregistrement');
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">Liste des Comptes</h2>
        <Button onClick={handleAdd}>Ajouter</Button>
      </div>

      <GlassCard>
        <table className="w-full text-white text-left">
          <thead>
            <tr className="border-b border-white/20">
              <th className="p-3">RIB</th>
              <th className="p-3">Type</th>
              <th className="p-3">Solde</th>
              <th className="p-3">Banque</th>
              <th className="p-3">Actions</th>
            </tr>
          </thead>
          <tbody>
            {comptes.map((compte) => (
              <tr
                key={compte.id}
                className="border-b border-white/10 hover:bg-white/5"
              >
                <td className="p-3">{compte.rib}</td>
                <td className="p-3">{compte.type}</td>
                <td className="p-3">{compte.solde} €</td>
                <td className="p-3">
                  {banques.find((b) => b.id === compte.banque_id)?.nom ||
                    'Non trouvé'}
                </td>
                <td className="p-3 space-x-2">
                  <Button size="sm" onClick={() => handleEdit(compte)}>
                    Modifier
                  </Button>
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => handleDelete(compte.id)}
                  >
                    Supprimer
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {/* Pagination simple */}
        <div className="flex justify-center gap-2 mt-4">
          <Button
            disabled={currentPage === 1}
            onClick={() => setCurrentPage(currentPage - 1)}
          >
            Précédent
          </Button>
          <span className="text-white/70 self-center">
            Page {currentPage} / {totalPages}
          </span>
          <Button
            disabled={currentPage === totalPages}
            onClick={() => setCurrentPage(currentPage + 1)}
          >
            Suivant
          </Button>
        </div>
      </GlassCard>

      {showForm && (
        <GlassCard className="p-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-white/80 mb-1">RIB</label>
              <input
                name="rib"
                type="text"
                value={formValues.rib}
                onChange={handleChange}
                className="glass-input w-full"
              />
            </div>
            <div>
              <label className="block text-white/80 mb-1">Type</label>
              <input
                name="type"
                type="text"
                value={formValues.type}
                onChange={handleChange}
                className="glass-input w-full"
              />
            </div>
            <div>
              <label className="block text-white/80 mb-1">Solde</label>
              <input
                name="solde"
                type="number"
                value={formValues.solde}
                onChange={handleChange}
                className="glass-input w-full"
              />
            </div>
            <div>
              <label className="block text-white/80 mb-1">Banque</label>
              <select
                name="banque_id"
                value={formValues.banque_id}
                onChange={handleChange}
                className="glass-input w-full"
              >
                <option value="">-- Choisir une banque --</option>
                {banques.map((banque) => (
                  <option key={banque.id} value={banque.id}>
                    {banque.nom}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex justify-end space-x-2">
              <Button type="submit">{editData ? 'Modifier' : 'Ajouter'}</Button>
              <Button variant="secondary" onClick={() => setShowForm(false)}>
                Annuler
              </Button>
            </div>
          </form>
        </GlassCard>
      )}
    </div>
  );
};

export default Comptes;
