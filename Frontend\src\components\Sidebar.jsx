import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X } from 'lucide-react';

const Sidebar = () => {
  const [open, setOpen] = useState(false);
  const location = useLocation();

  const links = [
    { name: 'Accueil', path: '/dashboard' },
    { name: 'Banques', path: '/banques' },
    { name: 'Compt<PERSON>', path: '/comptes' },
    { name: 'Personnels', path: '/personnels' },
    { name: 'Utilisateurs', path: '/users' },
  ];

  return (
    <div className="relative z-20">
      <button
        className="m-4 p-2 text-white focus:outline-none z-30"
        onClick={() => setOpen(!open)}
      >
        {open ? <X size={28} /> : <Menu size={28} />}
      </button>

      <div
        className={`fixed top-0 left-0 h-full w-64 bg-black/60 backdrop-blur-lg p-6 space-y-4 shadow-lg transition-transform duration-300 z-10 rounded-tr-3xl rounded-br-3xl ${
          open ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <h2 className="text-xl font-bold text-white mb-6">Menu</h2>
        {links.map(({ name, path }) => (
          <Link
            key={path}
            to={path}
            className={`block px-4 py-2 rounded-lg transition-colors text-white/80 hover:text-white hover:bg-white/10 ${
              location.pathname === path ? 'bg-white/10 text-white' : ''
            }`}
            onClick={() => setOpen(false)}
          >
            {name}
          </Link>
        ))}
      </div>
    </div>
  );
};

export default Sidebar;