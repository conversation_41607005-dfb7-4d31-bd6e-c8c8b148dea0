<?php
declare(strict_types=1);

namespace App\Controller;

/**
 * Banques Controller
 *
 * @property \App\Model\Table\BanquesTable $Banques
 */
class BanquesController extends ApiController
{
    /**
     * Index method
     */
    public function index()
    {
        try {
            $query = $this->Banques->find();
            $banques = $this->paginate($query);

            return $this->respondJson([
                'success' => true,
                'data' => $banques->toArray(),
                'pagination' => [
                    'page' => $this->request->getParam('paging.Banque.page'),
                    'count' => $this->request->getParam('paging.Banque.count'),
                    'total' => $this->request->getParam('paging.Banque.total'),
                    'pages' => $this->request->getParam('paging.Banque.pageCount')
                ]
            ]);
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    /**
     * View method
     */
    public function view($id = null)
    {
        try {
            if (!$id || !is_numeric($id)) {
                throw new \InvalidArgumentException('Invalid banque ID');
            }

            $banque = $this->Banques->get($id, contain: ['Comptes']);
            
            return $this->respondJson([
                'success' => true,
                'data' => $banque
            ]);
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    /**
     * Add method
     */
    public function add()
    {
        try {
            if (!$this->request->is('post')) {
                return $this->respondJson([
                    'success' => false,
                    'message' => 'Method not allowed'
                ], 405);
            }

            $requestData = $this->validateJsonRequest();
            
            if (empty($requestData)) {
                throw new \InvalidArgumentException('Request data is required');
            }

            $banque = $this->Banques->newEmptyEntity();
            $banque = $this->Banques->patchEntity($banque, $requestData);
            
            if ($this->Banques->save($banque)) {
                return $this->respondJson([
                    'success' => true,
                    'message' => 'Banque created successfully',
                    'data' => $banque
                ], 201);
            }

            return $this->respondJson([
                'success' => false,
                'message' => 'Banque could not be saved',
                'errors' => $banque->getErrors()
            ], 422);
            
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    /**
     * Edit method
     */
    public function edit($id = null)
    {
        try {
            if (!$this->request->is(['patch', 'post', 'put'])) {
                return $this->respondJson([
                    'success' => false,
                    'message' => 'Method not allowed'
                ], 405);
            }

            if (!$id || !is_numeric($id)) {
                throw new \InvalidArgumentException('Invalid banque ID');
            }

            $requestData = $this->validateJsonRequest();
            
            if (empty($requestData)) {
                throw new \InvalidArgumentException('Request data is required');
            }

            $banque = $this->Banques->get($id);
            $banque = $this->Banques->patchEntity($banque, $requestData);
            
            if ($this->Banques->save($banque)) {
                return $this->respondJson([
                    'success' => true,
                    'message' => 'Banque updated successfully',
                    'data' => $banque
                ]);
            }

            return $this->respondJson([
                'success' => false,
                'message' => 'Banque could not be updated',
                'errors' => $banque->getErrors()
            ], 422);
            
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    /**
     * Delete method
     */
    public function delete($id = null)
    {
        try {
            if (!$this->request->is(['post', 'delete'])) {
                return $this->respondJson([
                    'success' => false,
                    'message' => 'Method not allowed'
                ], 405);
            }

            if (!$id || !is_numeric($id)) {
                throw new \InvalidArgumentException('Invalid banque ID');
            }

            $banque = $this->Banques->get($id);
            
            if ($this->Banques->delete($banque)) {
                return $this->respondJson([
                    'success' => true,
                    'message' => 'Banque deleted successfully'
                ]);
            }

            return $this->respondJson([
                'success' => false,
                'message' => 'Banque could not be deleted'
            ], 422);
            
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    /**
     * Get banques with their accounts count
     */
    public function withAccountsCount()
    {
        try {
            $banques = $this->Banques->find()
                ->contain(['Comptes'])
                ->formatResults(function ($results) {
                    return $results->map(function ($banque) {
                        $banque->accounts_count = count($banque->comptes);
                        unset($banque->comptes); // Remove the actual comptes data, keep only count
                        return $banque;
                    });
                });

            return $this->respondJson([
                'success' => true,
                'data' => $banques->toArray()
            ]);
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    /**
     * Search banques by name
     */
    public function search()
    {
        try {
            if (!$this->request->is('get')) {
                return $this->respondJson([
                    'success' => false,
                    'message' => 'Method not allowed'
                ], 405);
            }

            $searchTerm = $this->request->getQuery('q');
            
            if (empty($searchTerm)) {
                throw new \InvalidArgumentException('Search term is required');
            }

            $banques = $this->Banques->find()
                ->where(['nom LIKE' => '%' . $searchTerm . '%'])
                ->limit(10)
                ->toArray();

            return $this->respondJson([
                'success' => true,
                'data' => $banques,
                'search_term' => $searchTerm
            ]);
            
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }
}