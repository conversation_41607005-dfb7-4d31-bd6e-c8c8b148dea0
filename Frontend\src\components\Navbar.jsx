import { useLocation } from 'react-router-dom';

const Navbar = () => {
  const location = useLocation();
  const isHomePage = location.pathname === '/dashboard';

  return (
    <header className="flex items-center justify-between px-6 py-4 glass-card shadow-md rounded-b-xl">
      {isHomePage && <h1 className="text-xl font-semibold text-white">Bienvenue 👋</h1>}
    </header>
  );
};

export default Navbar;
