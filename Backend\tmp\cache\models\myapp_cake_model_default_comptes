1753315568
O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:7:"comptes";s:11:" * _columns";a:4:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:3:"rib";a:8:{s:4:"type";s:6:"string";s:6:"length";i:30;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_0900_ai_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"banque_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:12:"personnel_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}}s:11:" * _typeMap";a:4:{s:2:"id";s:7:"integer";s:3:"rib";s:6:"string";s:9:"banque_id";s:7:"integer";s:12:"personnel_id";s:7:"integer";}s:11:" * _indexes";a:2:{s:9:"banque_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:9:"banque_id";}s:6:"length";a:0:{}}s:12:"personnel_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:12:"personnel_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:1:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"MyISAM";s:9:"collation";s:18:"utf8mb4_0900_ai_ci";}s:13:" * _temporary";b:0;}
