<?php
declare(strict_types=1);

namespace App\Controller;

class PersonnelsController extends ApiController
{
    public function index()
    {
        try {
            $query = $this->Personnels->find()->contain(['Users']);
            $personnels = $this->paginate($query);

            return $this->respondJson([
                'success' => true,
                'data' => $personnels->toArray(),
                'pagination' => [
                    'page' => $this->request->getParam('paging.Personnels.page'),
                    'count' => $this->request->getParam('paging.Personnels.count'),
                    'total' => $this->request->getParam('paging.Personnels.total'),
                    'pages' => $this->request->getParam('paging.Personnels.pageCount'),
                ]
            ]);
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    public function view($id = null)
    {
        try {
            if (!$id || !is_numeric($id)) {
                throw new \InvalidArgumentException('Invalid personnel ID');
            }

            $personnel = $this->Personnels->get($id, ['contain' => ['Users', 'Comptes']]);

            return $this->respondJson([
                'success' => true,
                'data' => $personnel
            ]);
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    public function add()
    {
        try {
            if (!$this->request->is('post')) {
                return $this->respondJson([
                    'success' => false,
                    'message' => 'Method not allowed'
                ], 405);
            }

            $requestData = $this->validateJsonRequest();
            if (empty($requestData)) {
                throw new \InvalidArgumentException('Request data is required');
            }

            $personnel = $this->Personnels->newEmptyEntity();
            $personnel = $this->Personnels->patchEntity($personnel, $requestData);

            if ($this->Personnels->save($personnel)) {
                return $this->respondJson([
                    'success' => true,
                    'message' => 'Personnel ajouté avec succès',
                    'data' => $personnel
                ], 201);
            }

            return $this->respondJson([
                'success' => false,
                'message' => 'Erreur lors de l\'ajout du personnel',
                'errors' => $personnel->getErrors()
            ], 422);
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    public function edit($id = null)
    {
        try {
            if (!$this->request->is(['patch', 'post', 'put'])) {
                return $this->respondJson([
                    'success' => false,
                    'message' => 'Method not allowed'
                ], 405);
            }

            if (!$id || !is_numeric($id)) {
                throw new \InvalidArgumentException('Invalid personnel ID');
            }

            $requestData = $this->validateJsonRequest();
            if (empty($requestData)) {
                throw new \InvalidArgumentException('Request data is required');
            }

            $personnel = $this->Personnels->get($id);
            $personnel = $this->Personnels->patchEntity($personnel, $requestData);

            if ($this->Personnels->save($personnel)) {
                return $this->respondJson([
                    'success' => true,
                    'message' => 'Personnel modifié avec succès',
                    'data' => $personnel
                ]);
            }

            return $this->respondJson([
                'success' => false,
                'message' => 'Erreur lors de la modification',
                'errors' => $personnel->getErrors()
            ], 422);
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    public function delete($id = null)
    {
        try {
            if (!$this->request->is(['post', 'delete'])) {
                return $this->respondJson([
                    'success' => false,
                    'message' => 'Method not allowed'
                ], 405);
            }

            if (!$id || !is_numeric($id)) {
                throw new \InvalidArgumentException('Invalid personnel ID');
            }

            $personnel = $this->Personnels->get($id);
            if ($this->Personnels->delete($personnel)) {
                return $this->respondJson([
                    'success' => true,
                    'message' => 'Personnel supprimé avec succès'
                ]);
            }

            return $this->respondJson([
                'success' => false,
                'message' => 'Erreur lors de la suppression'
            ], 422);
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }
}
