import axios from 'axios';

const API_BASE_URL = 'http://localhost:8765';

export const getComptes = async (page = 1) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/comptes?page=${page}`);
    return response.data;  // { comptes: [...], pagination: { page, pages, total } }
  } catch (error) {
    console.error('Erreur getComptes :', error);
    return { comptes: [], pagination: { page: 1, pages: 1, total: 0 } };
  }
};

export const deleteCompte = async (id) => {
  try {
    await axios.delete(`${API_BASE_URL}/comptes/${id}`);
  } catch (error) {
    console.error('Erreur deleteCompte :', error);
    throw error;
  }
};

export const createCompte = async (data) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/comptes`, data);
    return response.data;
  } catch (error) {
    console.error('Erreur createCompte :', error);
    throw error;
  }
};

export const updateCompte = async (id, data) => {
  try {
    const response = await axios.put(`${API_BASE_URL}/comptes/${id}`, data);
    return response.data;
  } catch (error) {
    console.error('Erreur updateCompte :', error);
    throw error;
  }
};
