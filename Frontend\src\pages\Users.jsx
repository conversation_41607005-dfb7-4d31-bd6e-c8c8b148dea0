import { useEffect, useState } from 'react';
import GlassCard from '../components/GlassCard';
import Button from '../components/Button';
import {
  getUsers,
  deleteUser,
  createUser,
  updateUser,
} from '../services/usersService';

const Users = () => {
  const [users, setUsers] = useState([]);
  const [pagination, setPagination] = useState({ page: 1, pages: 1, total: 0 });
  const [currentPage, setCurrentPage] = useState(1);
  const [showForm, setShowForm] = useState(false);
  const [editData, setEditData] = useState(null);
  const [formValues, setFormValues] = useState({
    username: '',
    password: '',
    role: '',
  });

  const fetchUsers = async (page = 1) => {
    try {
      const data = await getUsers(page);
      if (data) {
        setUsers(data.data || []);
        setPagination(data.pagination || { page: 1, pages: 1, total: 0 });
        setCurrentPage(data.pagination?.page || 1);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des utilisateurs:', error);
    }
  };

  useEffect(() => {
    fetchUsers(currentPage);
  }, [currentPage]);

  const handleChange = (e) => {
    setFormValues((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  };

  const handleAdd = () => {
    setEditData(null);
    setFormValues({ username: '', password: '', role: '' });
    setShowForm(true);
  };

  const handleEdit = (user) => {
    setEditData(user);
    setFormValues({
      username: user.username || '',
      password: '', // Ne pas préremplir le mot de passe
      role: user.role || '',
    });
    setShowForm(true);
  };

  const handleDelete = async (id) => {
    if (confirm('Voulez-vous vraiment supprimer cet utilisateur ?')) {
      await deleteUser(id);
      fetchUsers(currentPage);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (editData) {
      await updateUser(editData.id, formValues);
    } else {
      await createUser(formValues);
    }
    setShowForm(false);
    fetchUsers(currentPage);
  };

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= pagination.pages) {
      setCurrentPage(newPage);
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">Liste des Utilisateurs</h2>
        <Button onClick={handleAdd}>Ajouter</Button>
      </div>

      <GlassCard>
        <table className="w-full text-white text-left">
          <thead>
            <tr className="border-b border-white/20">
              <th className="p-3">Nom d'utilisateur</th>
              <th className="p-3">Rôle</th>
              <th className="p-3">Actions</th>
            </tr>
          </thead>
          <tbody>
            {users.map((user) => (
              <tr key={user.id} className="border-b border-white/10 hover:bg-white/5">
                <td className="p-3">{user.username}</td>
                <td className="p-3">{user.role}</td>
                <td className="p-3 space-x-2">
                  <Button size="sm" onClick={() => handleEdit(user)}>Modifier</Button>
                  <Button size="sm" variant="secondary" onClick={() => handleDelete(user.id)}>Supprimer</Button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </GlassCard>

      <div className="flex justify-center gap-2 mt-4">
        <Button disabled={currentPage === 1} onClick={() => handlePageChange(currentPage - 1)}>Précédent</Button>
        <span className="text-white/70 self-center">
          Page {currentPage} / {pagination.pages}
        </span>
        <Button disabled={currentPage === pagination.pages} onClick={() => handlePageChange(currentPage + 1)}>Suivant</Button>
      </div>

      {showForm && (
        <GlassCard className="p-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-white/80 mb-1">Nom d'utilisateur</label>
              <input
                name="username"
                type="text"
                value={formValues.username}
                onChange={handleChange}
                className="glass-input w-full"
                required
              />
            </div>
            <div>
              <label className="block text-white/80 mb-1">Mot de passe</label>
              <input
                name="password"
                type="password"
                value={formValues.password}
                onChange={handleChange}
                className="glass-input w-full"
                required={!editData} // obligatoire uniquement lors de l'ajout
              />
            </div>
            <div>
              <label className="block text-white/80 mb-1">Rôle</label>
              <input
                name="role"
                type="text"
                value={formValues.role}
                onChange={handleChange}
                className="glass-input w-full"
                required
              />
            </div>

            <div className="flex justify-end space-x-2">
              <Button type="submit">{editData ? 'Modifier' : 'Ajouter'}</Button>
              <Button variant="secondary" onClick={() => setShowForm(false)}>Annuler</Button>
            </div>
          </form>
        </GlassCard>
      )}
    </div>
  );
};

export default Users;
