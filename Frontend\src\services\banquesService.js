// services/banquesService.js
import axios from 'axios';

const API_BASE_URL = 'http://localhost:8765';

export const getBanques = async (page = 1) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/banques?page=${page}`);
    return response.data;
  } catch (error) {
    console.error('Erreur getBanques :', error);
    return null;
  }
};

export const createBanque = async (data) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/banques`, data);
    return response.data;
  } catch (error) {
    console.error('Erreur createBanque :', error);
    throw error;
  }
};

export const updateBanque = async (id, data) => {
  try {
    const response = await axios.put(`${API_BASE_URL}/banques/${id}`, data);
    return response.data;
  } catch (error) {
    console.error('Erreur updateBanque :', error);
    throw error;
  }
};

export const deleteBanque = async (id) => {
  try {
    await axios.delete(`${API_BASE_URL}/banques/${id}`);
  } catch (error) {
    console.error('Erreur deleteBanque :', error);
    throw error;
  }
};
