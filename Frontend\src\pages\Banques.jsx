import { useEffect, useState } from 'react';
import GlassCard from '../components/GlassCard';
import Button from '../components/Button';
import { motion } from 'framer-motion';
import {
  getBanques,
  createBanque,
  updateBanque,
  deleteBanque,
} from '../services/banquesService';

const Banques = () => {
  const [banques, setBanques] = useState([]);
  const [showForm, setShowForm] = useState(false);
  const [editData, setEditData] = useState(null);
  const [formValues, setFormValues] = useState({ nom: '' });
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const fetchBanques = async (page = 1) => {
    const data = await getBanques(page);
    console.log('data reçue:', data);

    if (data) {
      setBanques(data.data || []);
      setCurrentPage(data.pagination?.page ?? 1);
      setTotalPages(data.pagination?.pages ?? 1);
    } else {
      setBanques([]);
      setCurrentPage(1);
      setTotalPages(1);
    }
  };

  useEffect(() => {
    fetchBanques();
  }, []);

  const handleChange = (e) => {
    setFormValues({ ...formValues, [e.target.name]: e.target.value });
  };

  const handleAdd = () => {
    setEditData(null);
    setFormValues({ nom: '' });
    setShowForm(true);
  };

  const handleEdit = (banque) => {
    setEditData(banque);
    setFormValues({ nom: banque.nom });
    setShowForm(true);
  };

  const handleDelete = async (id) => {
    if (confirm('Voulez-vous vraiment supprimer cette banque ?')) {
      await deleteBanque(id);
      fetchBanques(currentPage);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editData) {
        await updateBanque(editData.id, formValues);
      } else {
        await createBanque(formValues);
      }
      setShowForm(false);
      fetchBanques(currentPage);
    } catch (error) {
      alert('Erreur lors de l’enregistrement');
    }
  };

  const handlePageChange = (page) => {
    fetchBanques(page);
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">Liste des Banques</h2>
        <Button onClick={handleAdd}>Ajouter</Button>
      </div>

      <GlassCard>
        <table className="w-full text-white text-left">
          <thead>
            <tr className="border-b border-white/20">
              <th className="p-3">ID</th>
              <th className="p-3">Nom</th>
              <th className="p-3">Actions</th>
            </tr>
          </thead>
          <tbody>
            {banques.map((banque) => (
              <tr key={banque.id} className="border-b border-white/10 hover:bg-white/5">
                <td className="p-3">{banque.id}</td>
                <td className="p-3">{banque.nom}</td>
                <td className="p-3 space-x-2">
                  <Button size="sm" onClick={() => handleEdit(banque)}>Modifier</Button>
                  <Button size="sm" variant="secondary" onClick={() => handleDelete(banque.id)}>Supprimer</Button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </GlassCard>

      <div className="flex justify-center gap-2">
        <Button disabled={currentPage === 1} onClick={() => handlePageChange(currentPage - 1)}>Précédent</Button>
        <span className="text-white/70 self-center">Page {currentPage} / {totalPages}</span>
        <Button disabled={currentPage === totalPages} onClick={() => handlePageChange(currentPage + 1)}>Suivant</Button>
      </div>

      {showForm && (
        <GlassCard className="p-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-white/80 mb-1">Nom de la banque</label>
              <input
                name="nom"
                type="text"
                value={formValues.nom}
                onChange={handleChange}
                className="glass-input w-full"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button type="submit">{editData ? 'Modifier' : 'Ajouter'}</Button>
              <Button variant="secondary" onClick={() => setShowForm(false)}>Annuler</Button>
            </div>
          </form>
        </GlassCard>
      )}
    </div>
  );
};

export default Banques;
