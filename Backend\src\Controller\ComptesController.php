<?php
declare(strict_types=1);

namespace App\Controller;

class ComptesController extends ApiController
{
    public function index()
    {
        try {
            $query = $this->Comptes->find()->contain(['Banques', 'Personnels']);
            $comptes = $this->paginate($query);

            return $this->respondJson([
                'success' => true,
                'data' => $comptes->toArray(),
                'pagination' => [
                    'page' => $this->request->getParam('paging.Comptes.page'),
                    'count' => $this->request->getParam('paging.Comptes.count'),
                    'total' => $this->request->getParam('paging.Comptes.total'),
                    'pages' => $this->request->getParam('paging.Comptes.pageCount'),
                ]
            ]);
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    public function view($id = null)
    {
        try {
            if (!$id || !is_numeric($id)) {
                throw new \InvalidArgumentException('Invalid compte ID');
            }

            $compte = $this->Comptes->get($id, ['contain' => ['Banques', 'Personnels']]);

            return $this->respondJson([
                'success' => true,
                'data' => $compte
            ]);
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    public function add()
    {
        try {
            if (!$this->request->is('post')) {
                return $this->respondJson([
                    'success' => false,
                    'message' => 'Method not allowed'
                ], 405);
            }

            $requestData = $this->validateJsonRequest();
            if (empty($requestData)) {
                throw new \InvalidArgumentException('Request data is required');
            }

            $compte = $this->Comptes->newEmptyEntity();
            $compte = $this->Comptes->patchEntity($compte, $requestData);

            if ($this->Comptes->save($compte)) {
                return $this->respondJson([
                    'success' => true,
                    'message' => 'Compte ajouté avec succès',
                    'data' => $compte
                ], 201);
            }

            return $this->respondJson([
                'success' => false,
                'message' => 'Erreur lors de l\'ajout du compte',
                'errors' => $compte->getErrors()
            ], 422);
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    public function edit($id = null)
    {
        try {
            if (!$this->request->is(['patch', 'post', 'put'])) {
                return $this->respondJson([
                    'success' => false,
                    'message' => 'Method not allowed'
                ], 405);
            }

            if (!$id || !is_numeric($id)) {
                throw new \InvalidArgumentException('Invalid compte ID');
            }

            $requestData = $this->validateJsonRequest();
            if (empty($requestData)) {
                throw new \InvalidArgumentException('Request data is required');
            }

            $compte = $this->Comptes->get($id);
            $compte = $this->Comptes->patchEntity($compte, $requestData);

            if ($this->Comptes->save($compte)) {
                return $this->respondJson([
                    'success' => true,
                    'message' => 'Compte modifié avec succès',
                    'data' => $compte
                ]);
            }

            return $this->respondJson([
                'success' => false,
                'message' => 'Erreur lors de la modification',
                'errors' => $compte->getErrors()
            ], 422);
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    public function delete($id = null)
    {
        try {
            if (!$this->request->is(['post', 'delete'])) {
                return $this->respondJson([
                    'success' => false,
                    'message' => 'Method not allowed'
                ], 405);
            }

            if (!$id || !is_numeric($id)) {
                throw new \InvalidArgumentException('Invalid compte ID');
            }

            $compte = $this->Comptes->get($id);
            if ($this->Comptes->delete($compte)) {
                return $this->respondJson([
                    'success' => true,
                    'message' => 'Compte supprimé avec succès'
                ]);
            }

            return $this->respondJson([
                'success' => false,
                'message' => 'Erreur lors de la suppression'
            ], 422);
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }
}
