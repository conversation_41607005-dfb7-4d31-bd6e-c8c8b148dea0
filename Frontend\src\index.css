@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Modern Glassmorphism Styles - Pure CSS */
.glass-card {
  position: relative;
  border-radius: 1rem;
  padding: 3rem 2.5rem;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow:
    0 25px 45px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.glass-input {
  position: relative;
  width: 100%;
  padding: 1rem 0;
  border: none;
  border-bottom: 2px solid rgba(255, 255, 255, 0.3);
  background: transparent;
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;
  outline: none;
}

.glass-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.glass-input:focus {
  border-bottom-color: rgba(255, 255, 255, 0.8);
}

.glass-input.error {
  border-bottom-color: #ff6b6b;
}

/* Password Input Container */
.password-input-container {
  position: relative;
  width: 100%;
}

.password-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: color 0.3s ease;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-toggle:hover {
  color: rgba(255, 255, 255, 0.9);
}

/* Field Error Messages */
.field-error {
  color: #ff6b6b;
  font-size: 0.875rem;
  margin-top: 0.5rem;
  margin-left: 0.25rem;
  animation: fadeIn 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.field-error::before {
  content: "⚠";
  font-size: 0.75rem;
}

/* Success Messages */
.success-message {
  color: #4ade80;
  font-size: 0.875rem;
  margin-top: 0.5rem;
  margin-left: 0.25rem;
  animation: fadeIn 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.success-message::before {
  content: "✓";
  font-size: 0.75rem;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading state for inputs */
.glass-input:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.glass-button {
  position: relative;
  overflow: hidden;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  font-weight: 600;
  color: #1a202c;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
  width: 100%;
  font-size: 1rem;
  margin-top: 1rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.glass-button:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.5);
}

.glass-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.glass-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* Dashboard specific styles */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.user-avatar {
  width: 5rem;
  height: 5rem;
  background: linear-gradient(135deg, #FF204E 0%, #FF6B9D 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  transition: all 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.1) rotate(360deg);
}

/* Button group styling */
.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

.button-group .glass-button {
  max-width: 200px;
  flex: 1;
  min-width: 150px;
}

/* Base Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-height: 100vh;
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  background:
    linear-gradient(135deg,
      #2d3748 0%,
      #4a5568 20%,
      #553c9a 40%,
      #667eea 60%,
      #764ba2 80%,
      #f093fb 100%
    );
  background-size: cover, cover;
  background-position: center, center;
  background-attachment: fixed;
  overflow-x: hidden;
}

/* Utility Classes */
.min-h-screen { min-height: 100vh; }
.flex { display: flex; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.p-4 { padding: 1rem; }
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.z-10 { z-index: 10; }
.-z-10 { z-index: -10; }
.w-full { width: 100%; }
.max-w-4xl { max-width: 56rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.mb-8 { margin-bottom: 2rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mt-8 { margin-top: 2rem; }
.mt-2 { margin-top: 0.5rem; }
.text-center { text-align: center; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.font-bold { font-weight: 700; }
.font-medium { font-weight: 500; }
.text-white { color: rgb(255 255 255); }
.text-white\/70 { color: rgb(255 255 255 / 0.7); }
.text-white\/60 { color: rgb(255 255 255 / 0.6); }
.text-white\/80 { color: rgb(255 255 255 / 0.8); }
.text-red-300 { color: rgb(252 165 165); }
.rounded-full { border-radius: 9999px; }
.rounded-lg { border-radius: 0.5rem; }
.w-16 { width: 4rem; }
.h-16 { height: 4rem; }
.w-20 { width: 5rem; }
.h-20 { height: 5rem; }
.w-5 { width: 1.25rem; }
.h-5 { height: 1.25rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.gap-2 { gap: 0.5rem; }
.gap-4 { gap: 1rem; }
.flex-wrap { flex-wrap: wrap; }
.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-white\/30 { border-color: rgb(255 255 255 / 0.3); }
.border-t-white { border-top-color: rgb(255 255 255); }
.animate-spin { animation: spin 1s linear infinite; }
.opacity-0 { opacity: 0; }
.opacity-100 { opacity: 1; }
.transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }

/* Hover states */
.hover\:text-white:hover { color: rgb(255 255 255); }

/* Background utilities */
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
.from-primary-400 { --tw-gradient-from: rgb(56 189 248); --tw-gradient-to: rgb(56 189 248 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.to-purple-500 { --tw-gradient-to: rgb(168 85 247); }
.from-green-400 { --tw-gradient-from: rgb(74 222 128); --tw-gradient-to: rgb(74 222 128 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.to-blue-500 { --tw-gradient-to: rgb(59 130 246); }
.bg-red-500\/20 { background-color: rgb(239 68 68 / 0.2); }
.border-red-400\/50 { border-color: rgb(248 113 113 / 0.5); }
.bg-green-500 { background-color: rgb(34 197 94); }

/* Form Container */
.form-container {
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
  padding: 2rem;
}

/* Custom Checkbox */
.custom-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 1.5rem 0;
}

.custom-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: rgba(255, 255, 255, 0.8);
}

.custom-checkbox label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  cursor: pointer;
}

/* Link Styles */
.auth-link {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

.auth-link:hover {
  color: rgba(255, 255, 255, 1);
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
}

/* Form Spacing */
.form-group {
  margin-bottom: 1.5rem;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1.5rem 0;
}

.form-footer {
  text-align: center;
  margin-top: 2rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

.form-footer p {
  margin: 0;
  line-height: 1.5;
}

/* Simple Background Pattern */
.bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  background-size: 100px 100px;
}

/* Keyframes */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 640px) {
  .form-container {
    max-width: 350px;
    padding: 1.5rem;
  }
}

/* Ensure backdrop-filter works */
@supports not (backdrop-filter: blur(20px)) {
  .glass-card {
    background: rgba(255, 255, 255, 0.2);
  }

  .glass-input {
    background: rgba(255, 255, 255, 0.15);
  }
}

/* Base Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-height: 100vh;
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  background: linear-gradient(135deg, #00224D 0%, #5D0E41 50%, #FF204E 100%);
  background-attachment: fixed;
  overflow-x: hidden;
}

/* Animated Background Elements */
.bg-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  opacity: 0.7;
  animation: float 8s ease-in-out infinite;
}

.bg-orb:nth-child(1) {
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.4) 0%, transparent 70%);
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.bg-orb:nth-child(2) {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(147, 51, 234, 0.4) 0%, transparent 70%);
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.bg-orb:nth-child(3) {
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(236, 72, 153, 0.4) 0%, transparent 70%);
  bottom: 20%;
  left: 50%;
  animation-delay: 4s;
}

/* Form Container */
.form-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  padding: 2rem;
}

/* Responsive Design */
@media (max-width: 640px) {
  .form-container {
    max-width: 350px;
    padding: 1.5rem;
  }
}

/* Ensure backdrop-filter works */
@supports not (backdrop-filter: blur(20px)) {
  .glass-card {
    background: rgba(255, 255, 255, 0.2);
  }

  .glass-input {
    background: rgba(255, 255, 255, 0.15);
  }
}
