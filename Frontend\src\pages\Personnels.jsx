import { useEffect, useState } from 'react';
import GlassCard from '../components/GlassCard';
import Button from '../components/Button';
import {
  getPersonnels,
  deletePersonnel,
  createPersonnel,
  updatePersonnel,
} from '../services/personnelsService';

const Personnels = () => {
  const [personnels, setPersonnels] = useState([]);
  const [pagination, setPagination] = useState({ page: 1, pages: 1, total: 0 });
  const [currentPage, setCurrentPage] = useState(1);
  const [showForm, setShowForm] = useState(false);
  const [editData, setEditData] = useState(null);
  const [formValues, setFormValues] = useState({
    nom: '',
    tel: '',
    mail: '',
    adresse: '',
    date_naissance: '',
  });

  const fetchPersonnels = async (page = 1) => {
    try {
      const data = await getPersonnels(page);
      if (data) {
        setPersonnels(data.data || []);
        setPagination(data.pagination || { page: 1, pages: 1, total: 0 });
        setCurrentPage(data.pagination?.page || 1);
      } else {
        setPersonnels([]);
        setPagination({ page: 1, pages: 1, total: 0 });
        setCurrentPage(1);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des personnels:', error);
      setPersonnels([]);
      setPagination({ page: 1, pages: 1, total: 0 });
      setCurrentPage(1);
    }
  };

  useEffect(() => {
    fetchPersonnels(currentPage);
  }, [currentPage]);

  const handleChange = (e) => {
    setFormValues((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  };

  const handleAdd = () => {
    setEditData(null);
    setFormValues({
      nom: '',
      tel: '',
      mail: '',
      adresse: '',
      date_naissance: '',
    });
    setShowForm(true);
  };

  const handleEdit = (personnel) => {
    setEditData(personnel);
    setFormValues({
      nom: personnel.nom || '',
      tel: personnel.tel || '',
      mail: personnel.mail || '',
      adresse: personnel.adresse || '',
      date_naissance: personnel.date_naissance ? personnel.date_naissance.slice(0, 10) : '',
    });
    setShowForm(true);
  };

  const handleDelete = async (id) => {
    if (confirm('Voulez-vous vraiment supprimer ce personnel ?')) {
      try {
        await deletePersonnel(id);
        fetchPersonnels(currentPage);
      } catch (error) {
        alert('Erreur lors de la suppression');
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editData) {
        await updatePersonnel(editData.id, formValues);
      } else {
        await createPersonnel(formValues);
      }
      setShowForm(false);
      fetchPersonnels(currentPage);
    } catch (error) {
      alert('Erreur lors de l’enregistrement');
    }
  };

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= pagination.pages) {
      setCurrentPage(newPage);
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">Liste des Personnels</h2>
        <Button onClick={handleAdd}>Ajouter</Button>
      </div>

      <GlassCard>
        <table className="w-full text-white text-left">
          <thead>
            <tr className="border-b border-white/20">
              <th className="p-3">ID</th>
              <th className="p-3">Nom</th>
              <th className="p-3">Téléphone</th>
              <th className="p-3">Email</th>
              <th className="p-3">Adresse</th>
              <th className="p-3">Date de naissance</th>
              <th className="p-3">Actions</th>
            </tr>
          </thead>
          <tbody>
            {personnels && personnels.length > 0 ? (
              personnels.map((personnel) => (
                <tr key={personnel.id} className="border-b border-white/10 hover:bg-white/5">
                  <td className="p-3">{personnel.id}</td>
                  <td className="p-3">{personnel.nom}</td>
                  <td className="p-3">{personnel.tel}</td>
                  <td className="p-3">{personnel.mail}</td>
                  <td className="p-3">{personnel.adresse}</td>
                  <td className="p-3">{personnel.date_naissance?.slice(0, 10)}</td>
                  <td className="p-3 space-x-2">
                    <Button size="sm" onClick={() => handleEdit(personnel)}>Modifier</Button>
                    <Button size="sm" variant="secondary" onClick={() => handleDelete(personnel.id)}>Supprimer</Button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="text-center text-white/60 p-3">
                  Aucun personnel trouvé.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </GlassCard>

      {/* Pagination simple */}
      <div className="flex justify-center gap-2 mt-4">
        <Button disabled={currentPage === 1} onClick={() => handlePageChange(currentPage - 1)}>Précédent</Button>
        <span className="text-white/70 self-center">
          Page {currentPage} / {pagination.pages}
        </span>
        <Button disabled={currentPage === pagination.pages} onClick={() => handlePageChange(currentPage + 1)}>Suivant</Button>
      </div>

      {showForm && (
        <GlassCard className="p-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-white/80 mb-1">Nom</label>
              <input
                name="nom"
                type="text"
                value={formValues.nom}
                onChange={handleChange}
                className="glass-input w-full"
                required
              />
            </div>
            <div>
              <label className="block text-white/80 mb-1">Téléphone</label>
              <input
                name="tel"
                type="tel"
                value={formValues.tel}
                onChange={handleChange}
                className="glass-input w-full"
              />
            </div>
            <div>
              <label className="block text-white/80 mb-1">Email</label>
              <input
                name="mail"
                type="email"
                value={formValues.mail}
                onChange={handleChange}
                className="glass-input w-full"
              />
            </div>
            <div>
              <label className="block text-white/80 mb-1">Adresse</label>
              <input
                name="adresse"
                type="text"
                value={formValues.adresse}
                onChange={handleChange}
                className="glass-input w-full"
              />
            </div>
            <div>
              <label className="block text-white/80 mb-1">Date de naissance</label>
              <input
                name="date_naissance"
                type="date"
                value={formValues.date_naissance}
                onChange={handleChange}
                className="glass-input w-full"
              />
            </div>

            <div className="flex justify-end space-x-2">
              <Button type="submit">{editData ? 'Modifier' : 'Ajouter'}</Button>
              <Button variant="secondary" onClick={() => setShowForm(false)}>Annuler</Button>
            </div>
          </form>
        </GlassCard>
      )}
    </div>
  );
};

export default Personnels;
