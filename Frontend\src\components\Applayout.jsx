import { Outlet } from 'react-router-dom';
import Background from './Background';
import Sidebar from './Sidebar';

const AppLayout = () => {
  return (
    <div className="min-h-screen flex bg-black text-white relative">
      <Background />
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <main className="p-6 flex-1 overflow-y-auto">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default AppLayout;