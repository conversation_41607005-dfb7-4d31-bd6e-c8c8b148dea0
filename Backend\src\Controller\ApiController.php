<?php
declare(strict_types=1);

namespace App\Controller;

use C<PERSON>\Controller\Controller;
use Cake\Event\EventInterface;

class Api<PERSON><PERSON>roller extends Controller
{
    
    public function beforeFilter(EventInterface $event)
    {
        parent::beforeFilter($event);
        
        // Set CORS headers
        $this->response = $this->response
            ->withHeader('Access-Control-Allow-Origin', '*')
            ->withHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE, OPTIONS')
            ->withHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept')
            ->withHeader('Access-Control-Max-Age', '3600');

        // Handle preflight OPTIONS requests
        if ($this->request->getMethod() === 'OPTIONS') {
            $this->autoRender = false;
            return $this->response->withStatus(200);
        }

        // BACKWARD COMPATIBILITY: Check if this is a modern controller
        if ($this->useModernApiPattern()) {
            $this->autoRender = false;
        }
        
        // Set default content type
        $this->response = $this->response->withType('application/json');
    }

    /**
     * Determine if this controller should use modern API pattern
     * Override this method in child controllers to opt-in to modern pattern
     */
    protected function useModernApiPattern(): bool
    {
        // Check if controller has been updated (has methods that use respondJson)
        $reflection = new \ReflectionClass($this);
        $methods = $reflection->getMethods(\ReflectionMethod::IS_PUBLIC);
        
        foreach ($methods as $method) {
            if (strpos($method->getDeclaringClass()->getName(), 'Controller') !== false) {
                $source = file_get_contents($method->getDeclaringClass()->getFileName());
                if (strpos($source, 'respondJson') !== false) {
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * Send JSON response with proper status code
     */
    protected function respondJson(array $data, int $status = 200): \Cake\Http\Response
    {
        $this->autoRender = false; // Ensure auto-render is disabled when using this method
        
        $this->response = $this->response
            ->withStatus($status)
            ->withType('application/json')
            ->withStringBody(json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
        
        return $this->response;
    }

    /**
     * Validate JSON request data
     */
    protected function validateJsonRequest(): array
    {
        // Check Content-Type header for POST/PUT/PATCH requests
        $method = $this->request->getMethod();
        if (in_array($method, ['POST', 'PUT', 'PATCH'])) {
            $contentType = $this->request->getHeaderLine('Content-Type');
            if (strpos($contentType, 'application/json') === false) {
                throw new \Exception('Content-Type must be application/json', 415);
            }
        }

        $requestData = $this->request->getData();
        
        // If no data received, try to parse raw JSON
        if (empty($requestData)) {
            $rawBody = $this->request->getBody()->getContents();
            if (!empty($rawBody)) {
                $requestData = json_decode($rawBody, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new \Exception('Invalid JSON format: ' . json_last_error_msg(), 400);
                }
            }
        }

        return $requestData ?: [];
    }

    /**
     * Handle errors consistently
     */
    protected function handleError(\Throwable $e): \Cake\Http\Response
    {
        $status = method_exists($e, 'getCode') && $e->getCode() > 0 ? $e->getCode() : 500;
        
        // Map common exception types to HTTP status codes
        if ($e instanceof \Cake\Datasource\Exception\RecordNotFoundException) {
            $status = 404;
        } elseif ($e instanceof \Cake\ORM\Exception\PersistenceFailedException) {
            $status = 422;
        } elseif ($e instanceof \InvalidArgumentException) {
            $status = 400;
        }

        return $this->respondJson([
            'success' => false,
            'message' => $e->getMessage(),
            'error_code' => $status
        ], $status);
    }
}